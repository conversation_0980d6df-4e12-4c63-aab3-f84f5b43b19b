"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { useTranslations, useLocale } from "next-intl";
import type { AIModel, GenerationResult, GenerationOptions, CostEstimate } from "../types";
import { getLocalActiveAIModels, getLocalAIModelsByType, type LocalAIModel } from "@/services/provider/model-manager";
import { getAiModelTranslation } from "@/services/page";

export function useAIGeneration(
  modelType?: string,
  onResultChange?: (result: GenerationResult | null) => void,
  onGeneratingChange?: (isGenerating: boolean) => void
) {
  const t = useTranslations("ai-dashboard");
  const locale = useLocale();
  const [selectedModel, setSelectedModel] = useState<AIModel | null>(null);
  const [allModels, setAllModels] = useState<LocalAIModel[]>([]);
  const [models, setModels] = useState<LocalAIModel[]>([]);
  const [modelsLoading, setModelsLoading] = useState(false); // 本地数据不需要loading
  const [modelsError, setModelsError] = useState<string | null>(null);
  const [prompt, setPrompt] = useState("");
  const [options, setOptions] = useState<GenerationOptions>({
    size: '1:1',
    aspectRatio: '1:1',
    variants: 1,
    temperature: 0.7,
    max_tokens: 1000,
    cdn: 'global',
    uploadedImages: [],
    referenceImages: []
  });
  const [loading, setLoading] = useState(false);
  const [costEstimate, setCostEstimate] = useState<CostEstimate | null>(null);
  const [userCredits, setUserCredits] = useState<number>(0);

  // 应用翻译到模型列表
  const applyTranslationsToModels = async (models: LocalAIModel[], locale: string): Promise<LocalAIModel[]> => {
    console.log('[applyTranslationsToModels] Function called with models:', models.length, 'locale:', locale);
    try {
      console.log('[applyTranslationsToModels] Loading translations for locale:', locale);
      const translations = await getAiModelTranslation(locale);
      console.log('Loaded translations object:', translations);
      console.log('translations.models:', translations.models);
      console.log('Available translation keys:', Object.keys(translations.models));

      const translatedModels = models.map(model => {
        // 只处理GRSAI提供商的模型
        if (model.provider === 'grsai') {
          const translationKey = `grsai.${model.model_id}`;
          const modelTranslation = translations.models[translationKey];

          console.log(`Processing ${model.model_id}: key=${translationKey}, found=${!!modelTranslation}`);

          if (modelTranslation) {
            console.log(`Applying translation for ${translationKey}:`, modelTranslation.name);
            return {
              ...model,
              model_name: modelTranslation.name || model.model_name || model.model_id, // 确保有名称显示
              description: modelTranslation.description || model.description
            };
          } else {
            console.warn(`No translation found for ${translationKey}`);
          }
        }

        return model;
      });

      console.log('[applyTranslationsToModels] Final translated models:',
        translatedModels.filter(m => m.provider === 'grsai').map(m => ({
          id: m.model_id,
          name: m.model_name,
          description: m.description
        }))
      );

      return translatedModels;
    } catch (error) {
      console.warn('Failed to apply translations:', error);
      return models;
    }
  };

  useEffect(() => {
    fetchUserCredits();
    loadLocalModels();
  }, [locale]);

  useEffect(() => {
    filterModels();
    if (selectedModel && modelType && selectedModel.model_type !== modelType) {
      setSelectedModel(null);
    }
  }, [modelType, allModels]);

  useEffect(() => {
    if (selectedModel && prompt) {
      estimateCost();
    }
  }, [selectedModel, prompt, options]);

  useEffect(() => {
    if (selectedModel) {
      // 不再在这里设置硬编码的默认值
      // 让DynamicOptionsConfig组件处理默认值加载
      console.log('[useAIGeneration] Model changed, letting DynamicOptionsConfig handle defaults');
    }
  }, [selectedModel]);

  const loadLocalModels = async () => {
    try {
      console.log('[loadLocalModels] Starting to load models...');
      setModelsError(null);
      setModelsLoading(true);

      // 获取本地模型
      console.log('[loadLocalModels] Getting local models for locale:', locale);
      const localModels = getLocalActiveAIModels(locale);
      console.log('[loadLocalModels] Got local models:', localModels.length);
      console.log('All local models:', localModels.map(m => ({ id: m.model_id, provider: m.provider, type: m.model_type })));

      // 应用翻译
      console.log('[loadLocalModels] About to apply translations...');
      const translatedModels = await applyTranslationsToModels(localModels, locale);
      console.log('[loadLocalModels] Translation applied, got models:', translatedModels.length);

      setAllModels(translatedModels);
      console.log('[loadLocalModels] Models set successfully');
    } catch (err) {
      console.error('[loadLocalModels] Failed to load local models:', err);
      setModelsError('Failed to load models');
    } finally {
      setModelsLoading(false);
      console.log('[loadLocalModels] Loading finished');
    }
  };

  const filterModels = () => {
    const filteredModels = modelType
      ? getLocalAIModelsByType(modelType, locale)
      : allModels;

    setModels(filteredModels);

    // 如果没有选中模型且有可用模型，优先选择GRSAI模型
    if (!selectedModel && filteredModels.length > 0) {
      // 优先选择GRSAI模型（支持翻译）
      const grsaiModel = filteredModels.find(m => m.provider === 'grsai');
      const modelToSelect = grsaiModel || filteredModels[0];

      console.log('[useAIGeneration] Auto-selecting model:', modelToSelect.model_id, 'provider:', modelToSelect.provider);
      setSelectedModel(modelToSelect as AIModel);
    }
  };

  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/get-user-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      const data = await response.json();
      if (data.code === 0) {
        setUserCredits(data.data.credits?.left_credits || 0);
      }
    } catch (error) {
      console.error('Failed to fetch user credits:', error);
    }
  };

  const estimateCost = async () => {
    if (!selectedModel || !prompt) return;

    try {
      const response = await fetch('/api/ai/estimate-cost', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: selectedModel.model_id,
          type: selectedModel.model_type,
          prompt,
          options
        })
      });
      const data = await response.json();
      if (data.code === 0) {
        setCostEstimate(data.data);
      }
    } catch (error) {
      console.error('Failed to estimate cost:', error);
    }
  };

  const handleGenerate = async () => {
    if (!selectedModel || !prompt.trim()) {
      toast.error(t("errors.invalid_input"));
      return;
    }

    if (costEstimate && !costEstimate.user_credits.can_afford) {
      toast.error(t("errors.insufficient_credits"));
      return;
    }

    setLoading(true);
    onGeneratingChange?.(true);
    onResultChange?.(null);

    try {
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: selectedModel.model_id,
          type: selectedModel.model_type,
          prompt,
          options
        })
      });

      const data = await response.json();

      if (data.code === 0) {
        if (data.data.status === 'pending' || data.data.status === 'running') {
          onResultChange?.(data.data);
          // 使用task_id进行轮询
          pollResult(data.data.task_id);
        } else {
          onResultChange?.(data.data);
          toast.success(t("status.success"));
          fetchUserCredits();
          onGeneratingChange?.(false);
        }
      } else {
        toast.error(data.msg || t("errors.generation_failed", { detail: "Unknown error" }));
        onGeneratingChange?.(false);
      }
    } catch (error) {
      toast.error(t("errors.network_error"));
      onGeneratingChange?.(false);
    } finally {
      setLoading(false);
    }
  };

  const pollResult = async (taskId: string) => {
    const maxAttempts = 60;
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await fetch('/api/ai/result', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ task_id: taskId })
        });

        const data = await response.json();

        if (data.code === 0) {
          onResultChange?.(data.data);

          if (data.data.status === 'success') {
            toast.success(t("status.success"));
            fetchUserCredits();
            onGeneratingChange?.(false);
            return;
          } else if (data.data.status === 'failed') {
            toast.error(t("errors.generation_failed", { detail: data.data.error?.detail || "Unknown error" }));
            onGeneratingChange?.(false);
            return;
          }
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          toast.error(t("errors.network_error"));
        }
      } catch (error) {
        console.error('Polling error:', error);
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        }
      }
    };

    poll();
  };

  const handleModelSelect = (modelId: string) => {
    const model = models.find(m => m.model_id === modelId);
    if (model) {
      // 将LocalAIModel转换为AIModel类型
      setSelectedModel(model as AIModel);
    }
  };

  return {
    selectedModel,
    models: models as AIModel[], // 类型转换
    modelsLoading,
    modelsError,
    prompt,
    setPrompt,
    options,
    setOptions,
    loading,
    costEstimate,
    userCredits,
    handleGenerate,
    handleModelSelect,
    fetchUserCredits
  };
}
