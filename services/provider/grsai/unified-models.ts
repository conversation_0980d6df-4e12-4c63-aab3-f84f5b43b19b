/**
 * GRSAI 统一模型配置
 * 包含模型基础信息和参数配置
 */

import { ParameterConfig } from '../types';

export enum ModelType {
  TEXT = 'text',
  IMAGE = 'image', 
  VIDEO = 'video',
  MULTIMODAL = 'multimodal'
}

export enum Provider {
  GRSAI = 'grsai',
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic'
}

export enum UnitType {
  TOKENS = 'tokens',
  IMAGES = 'images', 
  VIDEOS = 'videos'
}

export interface UnifiedModelConfig {
  // 基础模型信息
  id: string;
  name: string;
  type: ModelType;
  provider: Provider;
  apiEndpoint: string;
  creditsPerUnit: number;
  unitType: UnitType;
  isActive: boolean;
  description?: string;
  maxInputSize?: number;
  supportedFeatures?: string[];
  icon?: string;
  translationKey: string; // 翻译文件中的key

  // 参数配置
  parameters: ParameterConfig[];
  parameterGroups: {
    basic: string[];
    advanced: string[];
    expert: string[];
  };
}

/**
 * GRSAI 统一模型配置
 */
export const GRSAI_UNIFIED_MODELS: UnifiedModelConfig[] = [
  // 文本生成模型
  {
    id: 'gemini-2.5-pro',
    name: '', // 将从翻译文件获取
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 10,
    unitType: UnitType.TOKENS,
    isActive: true,
    translationKey: 'grsai.gemini-2-5-pro',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'conversation', 'analysis'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 8192,
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'basic'
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['top_p', 'stream'],
      expert: []
    }
  },
  
  {
    id: 'gemini-2.5-flash',
    name: '', // 将从翻译文件获取
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 5,
    unitType: UnitType.TOKENS,
    isActive: true,
    translationKey: 'grsai.gemini-2-5-flash',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'conversation', 'fast_response'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 4096,
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'basic'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['stream'],
      expert: []
    }
  },
  
  {
    id: 'gemini-2.5-flash-lite',
    name: '', // 将从翻译文件获取
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 2,
    unitType: UnitType.TOKENS,
    isActive: true,
    translationKey: 'grsai.gemini-2-5-flash-lite',
    maxInputSize: 64000,
    supportedFeatures: ['text_generation', 'basic_conversation'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 500,
        min: 1,
        max: 2048,
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'basic'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: [],
      expert: []
    }
  },
  
  {
    id: 'gpt-4o-mini',
    name: '', // 将从翻译文件获取
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 8,
    unitType: UnitType.TOKENS,
    isActive: true,
    translationKey: 'grsai.gpt-4o-mini',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'conversation', 'reasoning'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 4096,
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'basic'
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['top_p', 'stream'],
      expert: []
    }
  },

  // 多模态模型
  {
    id: 'o4-mini-all',
    name: '', // 将从翻译文件获取
    type: ModelType.MULTIMODAL,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 12,
    unitType: UnitType.TOKENS,
    isActive: true,
    translationKey: 'grsai.o4-mini-all',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'vision', 'multimodal', 'reasoning'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 2048,
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'vision_detail',
        type: 'select',
        required: false,
        default: 'auto',
        options: [
          { value: 'auto' },
          { value: 'low' },
          { value: 'high' }
        ],
        group: 'advanced',
        condition: {
          field: 'uploadedImages',
          value: [],
          operator: 'not_empty'
        }
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature', 'uploadedImages'],
      advanced: ['vision_detail', 'stream'],
      expert: []
    }
  },

  {
    id: 'gpt-4o-all',
    name: '', // 将从翻译文件获取
    type: ModelType.MULTIMODAL,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 20,
    unitType: UnitType.TOKENS,
    isActive: true,
    translationKey: 'grsai.gpt-4o-all',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'vision', 'multimodal', 'advanced_reasoning', 'code_generation'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 4096,
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'vision_detail',
        type: 'select',
        required: false,
        default: 'auto',
        options: [
          { value: 'auto' },
          { value: 'low' },
          { value: 'high' }
        ],
        group: 'advanced',
        condition: {
          field: 'uploadedImages',
          value: [],
          operator: 'not_empty'
        }
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature', 'uploadedImages'],
      advanced: ['vision_detail', 'top_p', 'stream'],
      expert: []
    }
  }
];
